'use client';

import React, { useState, useEffect } from 'react';

// 聊天消息接口
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface MessageItemProps {
  message: ChatMessage;
}

export const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  // 计时器状态
  const [elapsedTime, setElapsedTime] = useState(0);

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (message.isStreaming) {
      // 重置计时器
      setElapsedTime(0);
      // 启动计时器，每100ms更新一次
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 0.1);
      }, 100);
    } else {
      // 停止计时器
      if (interval) {
        clearInterval(interval);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [message.isStreaming]);

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6`}>
      <div className={`flex max-w-[85%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* 头像 */}
        <div className={`flex-shrink-0 ${isUser ? 'ml-4' : 'mr-4'}`}>
          <div className={`w-10 h-10 rounded-2xl flex items-center justify-center shadow-md ${
            isUser
              ? 'bg-gradient-to-br from-[#00C250] to-[#00a843] text-white'
              : 'bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] text-white'
          }`}>
            <span className="material-icons text-lg">
              {isUser ? 'person' : 'smart_toy'}
            </span>
          </div>
        </div>

        {/* 消息内容 */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          {/* 消息气泡和计时器的容器 */}
          <div className="flex items-start space-x-2">
            {/* 消息气泡 */}
            <div className={`relative px-5 py-4 rounded-2xl max-w-full shadow-sm ${
              isUser
                ? 'bg-gradient-to-br from-[#00C250] to-[#00a843] text-white rounded-br-lg'
                : 'bg-white text-gray-800 rounded-bl-lg border border-gray-100'
            }`}>
              {/* 流式生成指示器 */}
              {message.isStreaming && (
                <div className="flex items-center space-x-2 mb-3">
                  <div className="flex space-x-1">
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`} style={{ animationDelay: '0.1s' }}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`} style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className={`text-xs ${isUser ? 'text-white text-opacity-80' : 'text-gray-500'}`}>
                    AI正在思考...
                  </span>
                </div>
              )}

              {/* 消息文本 */}
              <div className="whitespace-pre-wrap break-words leading-relaxed">
                {message.content || (message.isStreaming ? '' : '消息生成失败')}
              </div>

              {/* 流式生成光标 */}
              {message.isStreaming && message.content && (
                <span className={`inline-block w-0.5 h-5 ml-1 animate-pulse ${isUser ? 'bg-white' : 'bg-gray-600'}`}></span>
              )}
            </div>

            {/* 计时器显示在气泡右边 */}
            {!isUser && message.isStreaming && (
              <div className="flex items-center mt-1">
                <div className="bg-gray-100 px-2 py-1 rounded-full text-xs text-gray-600 whitespace-nowrap">
                  {elapsedTime.toFixed(1)}秒
                </div>
              </div>
            )}
          </div>

          {/* 时间戳 */}
          <div className={`text-xs text-gray-400 mt-2 px-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    </div>
  );
};
