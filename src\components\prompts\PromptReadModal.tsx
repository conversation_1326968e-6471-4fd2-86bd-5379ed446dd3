/**
 * 提示词阅读模态窗口组件
 * 用于展示提示词的详细信息（仅读取zhanshi表数据）
 */
import React, { useEffect } from 'react';
import { Modal } from '@/components/common/modals';

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作', color: 'bg-[#5a9d6b] text-white', icon: 'create', gradient: 'from-[#5a9d6b] to-[#4a8d5b]' },
  'ai_polishing': { label: 'AI润色', color: 'bg-[#7D85CC] text-white', icon: 'auto_fix_high', gradient: 'from-[#7D85CC] to-[#6F9CE0]' },
  'character': { label: '角色塑造', color: 'bg-[#9C6FE0] text-white', icon: 'person', gradient: 'from-[#9C6FE0] to-[#8C5FD0]' },
  'worldbuilding': { label: '世界观构建', color: 'bg-[#E06F9C] text-white', icon: 'public', gradient: 'from-[#E06F9C] to-[#D05F8C]' },
  'plot': { label: '情节设计', color: 'bg-[#6F9CE0] text-white', icon: 'timeline', gradient: 'from-[#6F9CE0] to-[#5F8CD0]' },
  'outline': { label: '大纲规划', color: 'bg-[#E0976F] text-white', icon: 'format_list_bulleted', gradient: 'from-[#E0976F] to-[#D0875F]' }
} as const;

// 提示词接口
interface Prompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  category: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  author_display_id?: string;
}

interface PromptReadModalProps {
  isOpen: boolean;
  onClose: () => void;
  prompt: Prompt | null;
}

export default function PromptReadModal({
  isOpen,
  onClose,
  prompt
}: PromptReadModalProps) {
  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!prompt) return null;

  // 获取提示词类型信息
  const typeInfo = promptTypeMap[prompt.type as keyof typeof promptTypeMap] || {
    label: '未知类型',
    color: 'bg-gray-500 text-white',
    icon: 'help_outline',
    gradient: 'from-gray-500 to-gray-600'
  };

  // 格式化日期显示
  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知时间';
    try {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '未知时间';
    }
  };

  // 获取分类显示信息
  const getCategoryInfo = (category: string) => {
    switch (category) {
      case 'official':
        return {
          label: '官方提示词',
          color: 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 border-blue-200',
          icon: 'verified'
        };
      case 'userprompt':
        return {
          label: '用户提示词',
          color: 'bg-gradient-to-r from-green-100 to-green-50 text-green-700 border-green-200',
          icon: 'person'
        };
      default:
        return {
          label: '未知分类',
          color: 'bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 border-gray-200',
          icon: 'help_outline'
        };
    }
  };

  const categoryInfo = getCategoryInfo(prompt.category);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="提示词详情"
      maxWidth="max-w-4xl"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.03)] to-[rgba(90,157,107,0.05)] rounded-xl"></div>
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.08)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative h-full overflow-y-auto">
        <div className="space-y-6 p-1">
          {/* 顶部标题区域 */}
          <div className="relative">
            {/* 装饰性胶带 */}
            <div className="absolute -top-3 left-6 w-20 h-8 bg-gradient-to-r from-yellow-300/70 to-yellow-400/70 rounded-sm transform -rotate-2 shadow-sm z-10"></div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-[rgba(120,180,140,0.2)] p-6 shadow-lg">
              <div className="flex flex-col sm:flex-row items-start justify-between mb-4 gap-4">
                <div className="flex-1 min-w-0">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 font-ma-shan leading-tight mb-3">
                    {prompt.title}
                  </h1>

                  {/* 标签区域 */}
                  <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                    {/* 类型标签 */}
                    <div className={`inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium shadow-md ${typeInfo.color} transform hover:scale-105 transition-all duration-300`}>
                      <div className="w-4 sm:w-5 h-4 sm:h-5 rounded-full bg-white/20 flex items-center justify-center mr-1 sm:mr-2">
                        <span className="material-icons text-xs text-white">
                          {typeInfo.icon}
                        </span>
                      </div>
                      <span>{typeInfo.label}</span>
                    </div>

                    {/* 分类标签 */}
                    <div className={`inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium border ${categoryInfo.color} shadow-sm`}>
                      <span className="material-icons text-xs sm:text-sm mr-1 sm:mr-2">
                        {categoryInfo.icon}
                      </span>
                      <span>{categoryInfo.label}</span>
                    </div>
                  </div>
                </div>

                {/* 右侧元信息 */}
                <div className="flex flex-col gap-2 text-xs sm:text-sm text-gray-600 flex-shrink-0">
                  <div className="flex items-center">
                    <span className="material-icons text-sm mr-1 text-blue-500">schedule</span>
                    <span>{formatDate(prompt.created_at)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="material-icons text-sm mr-1 text-green-500">update</span>
                    <span>{formatDate(prompt.updated_at)}</span>
                  </div>
                  {(prompt.author_display_id || prompt.created_by) && (
                    <div className="flex items-center">
                      <span className="material-icons text-sm mr-1 text-purple-500">person</span>
                      <span className="truncate max-w-32">
                        {prompt.author_display_id || prompt.created_by}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 描述内容区域 */}
          <div className="relative">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-[rgba(120,180,140,0.2)] p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-[#E0C56F] to-[#D0B55F] rounded-full flex items-center justify-center mr-3 shadow-md">
                  <span className="material-icons text-white text-sm">description</span>
                </div>
                <h2 className="text-xl font-semibold text-gray-800 font-ma-shan">提示词介绍</h2>
              </div>

              <div className="min-h-[200px]">
                {prompt.description ? (
                  <div className="prose prose-sm max-w-none">
                    <div className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-200/50 shadow-inner">
                      <p className="whitespace-pre-wrap text-gray-700 leading-relaxed text-base">
                        {prompt.description}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-48 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-200/50">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                        <span className="material-icons text-2xl text-gray-500">description</span>
                      </div>
                      <p className="text-gray-500 font-medium">暂无详细介绍</p>
                      <p className="text-gray-400 text-sm mt-1">这个提示词还没有添加描述信息</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 元信息区域 */}
          <div className="relative">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-[rgba(120,180,140,0.2)] p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-[#6F9CE0] to-[#5F8CD0] rounded-full flex items-center justify-center mr-3 shadow-md">
                  <span className="material-icons text-white text-sm">info</span>
                </div>
                <h2 className="text-xl font-semibold text-gray-800 font-ma-shan">详细信息</h2>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                {/* 创建时间 */}
                <div className="flex items-center p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-blue-25 rounded-xl border border-blue-200/50">
                  <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                    <span className="material-icons text-white text-xs sm:text-sm">schedule</span>
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-blue-700">创建时间</p>
                    <p className="text-blue-600 text-xs sm:text-sm truncate">{formatDate(prompt.created_at)}</p>
                  </div>
                </div>

                {/* 更新时间 */}
                <div className="flex items-center p-3 sm:p-4 bg-gradient-to-r from-green-50 to-green-25 rounded-xl border border-green-200/50">
                  <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                    <span className="material-icons text-white text-xs sm:text-sm">update</span>
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-green-700">更新时间</p>
                    <p className="text-green-600 text-xs sm:text-sm truncate">{formatDate(prompt.updated_at)}</p>
                  </div>
                </div>

                {/* 创建者信息 */}
                {(prompt.author_display_id || prompt.created_by) && (
                  <div className="flex items-center p-3 sm:p-4 bg-gradient-to-r from-purple-50 to-purple-25 rounded-xl border border-purple-200/50 sm:col-span-2">
                    <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                      <span className="material-icons text-white text-xs sm:text-sm">person</span>
                    </div>
                    <div className="min-w-0">
                      <p className="text-xs sm:text-sm font-medium text-purple-700">创建者</p>
                      <p className="text-purple-600 text-xs sm:text-sm truncate">
                        {prompt.author_display_id || prompt.created_by}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>


        </div>
      </div>
    </Modal>
  );
}
