'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MessageList } from './MessageList';
import { ChatInput } from './ChatInput';
import { ModelSelector } from './ModelSelector';
import { ChatPromptModal } from './ChatPromptModal';
import { generateAIContentStream, MODELS, Message, Usage } from '@/lib/AIserver';
import { useAuth } from '@/hooks/useAuth';

// 聊天消息接口
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export const ChatInterface: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedModel, setSelectedModel] = useState(MODELS.LLM_TEST);
  const [error, setError] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [showPromptModal, setShowPromptModal] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 生成消息ID
  const generateMessageId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  // 发送消息
  const handleSendMessage = async (content: string) => {
    if (!isAuthenticated) {
      setError('请先登录后再使用AI对话功能');
      return;
    }

    if (!content.trim()) return;

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: generateMessageId(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setError('');
    setIsGenerating(true);

    // 创建AI回复消息
    const assistantMessage: ChatMessage = {
      id: generateMessageId(),
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // 构建消息历史（包含上下文）
      const messageHistory: Message[] = [];

      // 如果有系统提示词，添加到消息历史开头
      if (systemPrompt.trim()) {
        messageHistory.push({
          role: 'system',
          content: systemPrompt.trim()
        });
      }

      // 添加历史消息
      messageHistory.push(...messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })));

      // 添加当前用户消息
      messageHistory.push({
        role: 'user',
        content: content.trim()
      });

      // 流式生成处理 - 后端已经逐字符发送，前端直接显示

      // 调用AI生成
      await generateAIContentStream(
        messageHistory,
        { model: selectedModel },
        (chunk) => {
          // 后端已经逐字符发送，前端直接显示
          if (!chunk) return;

          // 直接更新消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === assistantMessage.id
              ? { ...msg, content: msg.content + chunk }
              : msg
          ));
        },
        (usage: Usage) => {
          // 处理usage信息（可选）
          console.log('Token usage:', usage);
        }
      );

      // 完成流式生成
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, isStreaming: false }
          : msg
      ));

    } catch (error: any) {
      console.error('AI生成失败:', error);

      // 检查是否是用户主动停止
      if (error.name === 'AbortError') {
        // 用户主动停止，保留已生成的内容
        setMessages(prev => prev.map(msg =>
          msg.id === assistantMessage.id
            ? { ...msg, isStreaming: false, content: msg.content + '\n\n[生成已停止]' }
            : msg
        ));
      } else {
        // 在AI消息气泡中显示错误代码
        const errorCode = error.message || '500';
        setMessages(prev => prev.map(msg =>
          msg.id === assistantMessage.id
            ? { ...msg, isStreaming: false, content: `错误：${errorCode}` }
            : msg
        ));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // 清空对话
  const handleClearChat = () => {
    if (confirm('确定要清空所有对话记录吗？')) {
      setMessages([]);
      setError('');
      // 同时清除localStorage中的消息
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('chatMessages');
        } catch (error) {
          console.error('清除消息存储失败:', error);
        }
      }
    }
  };

  // 处理提示词保存
  const handlePromptSave = (prompt: string) => {
    setSystemPrompt(prompt);
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('chatSystemPrompt', prompt);
      } catch (error) {
        console.error('保存提示词失败:', error);
      }
    }
  };

  // 从localStorage加载数据
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // 加载提示词
        const savedPrompt = localStorage.getItem('chatSystemPrompt');
        if (savedPrompt) {
          setSystemPrompt(savedPrompt);
        }

        // 加载消息历史
        const savedMessages = localStorage.getItem('chatMessages');
        if (savedMessages) {
          const parsedMessages = JSON.parse(savedMessages);
          // 恢复Date对象
          const messagesWithDates = parsedMessages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));
          setMessages(messagesWithDates);
        }

        // 加载选中的模型
        const savedModel = localStorage.getItem('chatSelectedModel');
        if (savedModel && Object.values(MODELS).includes(savedModel as any)) {
          setSelectedModel(savedModel as any);
        }
      } catch (error) {
        console.error('加载聊天数据失败:', error);
      }
    }
  }, []);

  // 保存消息到localStorage
  useEffect(() => {
    if (typeof window !== 'undefined' && messages.length > 0) {
      try {
        localStorage.setItem('chatMessages', JSON.stringify(messages));
      } catch (error) {
        console.error('保存消息失败:', error);
      }
    }
  }, [messages]);

  // 保存选中的模型到localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('chatSelectedModel', selectedModel);
      } catch (error) {
        console.error('保存模型选择失败:', error);
      }
    }
  }, [selectedModel]);

  // 如果用户未登录，显示登录提示
  if (!isAuthenticated) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] flex items-center justify-center text-white">
            <span className="material-icons text-2xl">chat</span>
          </div>
          <h3 className="text-xl font-medium text-text-dark mb-2">AI智能对话</h3>
          <p className="text-text-medium mb-4">请登录后开始与AI对话</p>
          <button
            onClick={() => window.location.href = '/login'}
            className="px-6 py-2 bg-[#00C250] text-white rounded-lg hover:bg-[#00a843] transition-colors"
          >
            立即登录
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* 顶部工具栏 */}
      <div className="border-b border-gray-200 bg-white">
        <div className="max-w-4xl mx-auto flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              disabled={isGenerating}
            />
            <button
              onClick={() => setShowPromptModal(true)}
              disabled={isGenerating}
              className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title={systemPrompt ? '已设置系统提示词' : '设置系统提示词'}
            >
              <span className={`material-icons text-sm mr-1 ${systemPrompt ? 'text-[#00C250]' : ''}`}>
                psychology
              </span>
              提示词设置
              {systemPrompt && (
                <span className="ml-1 w-2 h-2 bg-[#00C250] rounded-full"></span>
              )}
            </button>
          </div>
          <button
            onClick={handleClearChat}
            disabled={isGenerating || messages.length === 0}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="material-icons text-sm mr-1">delete_sweep</span>
            清空对话
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="max-w-4xl mx-auto px-4 mt-4">
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {error}
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto chat-scroll">
        <div className="w-full">
          <MessageList messages={messages} />
        </div>
        <div ref={messagesEndRef} />
      </div>

      {/* 输入框 */}
      <div className="border-t border-gray-200">
        <div className="w-full">
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={isGenerating}
            placeholder={isGenerating ? 'AI正在思考中...' : '输入消息...'}
          />
        </div>
      </div>

      {/* 提示词设置模态窗口 */}
      <ChatPromptModal
        isOpen={showPromptModal}
        onClose={() => setShowPromptModal(false)}
        onSave={handlePromptSave}
        initialPrompt={systemPrompt}
      />
    </div>
  );
};
