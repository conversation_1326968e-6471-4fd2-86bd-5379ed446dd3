/**
 * 提示词表单模态窗口组件
 * 用于创建和编辑用户提示词
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (prompt: UserPrompt) => void;
  prompt?: UserPrompt | null; // 编辑时传入现有提示词
  promptType: string; // 提示词类型
}

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作' },
  'ai_polishing': { label: 'AI润色' },
  'character': { label: '角色塑造' },
  'worldbuilding': { label: '世界观构建' }
} as const;

export default function PromptFormModal({
  isOpen,
  onClose,
  onSave,
  prompt,
  promptType
}: PromptFormModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: promptType,
    content: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当模态窗口打开或提示词数据变化时，初始化表单
  useEffect(() => {
    if (isOpen) {
      if (prompt) {
        // 编辑模式：填充现有数据
        setFormData({
          title: prompt.title || '',
          description: prompt.description || '',
          type: prompt.type || promptType,
          content: prompt.content || ''
        });
      } else {
        // 创建模式：重置表单
        setFormData({
          title: '',
          description: '',
          type: promptType,
          content: ''
        });
      }
      setError(null);
    }
  }, [isOpen, prompt, promptType]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('请输入提示词标题');
      return;
    }
    
    if (!formData.content.trim()) {
      setError('请输入提示词内容');
      return;
    }

    if (formData.content.length > 10000) {
      setError('提示词内容不能超过10000字');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const requestData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        content: formData.content.trim()
      };

      // 如果是编辑模式，添加ID
      if (prompt?.id) {
        (requestData as any).id = prompt.id;
      }

      const response = await fetch('/api/prompt', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '操作失败');
      }

      if (data.success) {
        // 调用父组件的保存回调
        onSave(data.data);
        onClose();
      } else {
        throw new Error(data.error || '操作失败');
      }
    } catch (error) {
      console.error('保存提示词失败:', error);
      setError(error instanceof Error ? error.message : '保存失败，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // 清除错误信息
    if (error) {
      setError(null);
    }
  };

  const isEditMode = !!prompt?.id;
  const modalTitle = isEditMode ? '编辑提示词' : '创建提示词';
  const typeLabel = promptTypeMap[promptType as keyof typeof promptTypeMap]?.label || promptType;

  // 按钮组件
  const footerButtons = (
    <div className="flex justify-end space-x-4">
      <button
        type="button"
        onClick={onClose}
        className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-all duration-300 font-medium hover:bg-gray-100/50 rounded-xl"
        disabled={isLoading}
      >
        取消
      </button>
      <button
        type="submit"
        form="prompt-form"
        disabled={isLoading}
        className={`relative px-8 py-3 rounded-xl font-medium transition-all duration-300 transform flex items-center ${
          isLoading
            ? 'bg-gray-400 text-white cursor-not-allowed opacity-70'
            : 'bg-gradient-to-r from-[#00C250] to-[#00A844] text-white hover:from-[#00A844] hover:to-[#008A3A] hover:scale-105 hover:shadow-lg hover:shadow-[rgba(0,194,80,0.3)] active:scale-95'
        }`}
      >
        {!isLoading && (
          <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
        )}
        <span className="relative z-10 flex items-center">
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2"></div>
              保存中...
            </>
          ) : (
            <>
              <span className="material-icons text-sm mr-2">
                {isEditMode ? 'edit' : 'add_circle'}
              </span>
              {isEditMode ? '更新' : '创建'}
            </>
          )}
        </span>
      </button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${modalTitle} - ${typeLabel}`}
      maxWidth="max-w-4xl"
      footer={footerButtons}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.03)] to-[rgba(90,157,107,0.05)] rounded-xl"></div>
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.08)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative h-full overflow-y-auto">
        <form id="prompt-form" onSubmit={handleSubmit} className="space-y-6 p-1">
          {/* 错误提示 */}
          {error && (
            <div className="relative p-4 bg-gradient-to-r from-red-50 to-red-100/80 backdrop-blur-sm border border-red-200/60 rounded-xl shadow-sm">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="material-icons text-white text-sm">error_outline</span>
                </div>
                <p className="text-red-700 font-medium">{error}</p>
              </div>
              <div className="absolute top-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
            </div>
          )}

          {/* 标题输入 */}
          <div className="relative">
            <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
              <span className="material-icons text-sm mr-2 text-[#5a9d6b]">title</span>
              提示词标题
              <span className="text-red-500 ml-1">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(90,157,107,0.3)] focus:border-[rgba(90,157,107,0.4)] focus:bg-white/95 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm hover:shadow-md font-medium"
                placeholder="为您的提示词起一个有意义的名称..."
                maxLength={100}
                required
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                {formData.title.length}/100
              </div>
            </div>
          </div>

          {/* 描述输入 */}
          <div className="relative">
            <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
              <span className="material-icons text-sm mr-2 text-[#7D85CC]">description</span>
              提示词描述
              <span className="text-gray-400 text-xs ml-2">(可选)</span>
            </label>
            <div className="relative">
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(125,133,204,0.3)] focus:border-[rgba(125,133,204,0.4)] focus:bg-white/95 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm hover:shadow-md resize-none font-medium"
                placeholder="简要描述这个提示词的用途和特点..."
                rows={3}
                maxLength={500}
              />
              <div className="absolute right-3 bottom-3 text-xs text-gray-400">
                {formData.description.length}/500
              </div>
            </div>
          </div>

          {/* 内容输入 */}
          <div className="relative">
            <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
              <span className="material-icons text-sm mr-2 text-[#E0C56F]">code</span>
              提示词内容
              <span className="text-red-500 ml-1">*</span>
            </label>
            <div className="relative">
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                className="w-full px-4 py-4 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(224,197,111,0.3)] focus:border-[rgba(224,197,111,0.4)] focus:bg-white/95 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm hover:shadow-md resize-none font-mono text-sm leading-relaxed custom-scrollbar"
                placeholder="请输入详细的提示词内容，包括指令、示例和要求..."
                rows={10}
                maxLength={10000}
                required
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23rgba(224,197,111,0.1)' stroke-width='1'%3E%3Cpath d='M3 3h18v18H3z'/%3E%3C/g%3E%3C/svg%3E")`,
                  backgroundSize: '24px 24px',
                  backgroundPosition: '12px 12px'
                }}
              />
              <div className="absolute right-4 bottom-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-md text-xs text-gray-500 shadow-sm border border-gray-200/50">
                {formData.content.length}/10000 字
              </div>

              {/* 内容提示 */}
              {formData.content.length === 0 && (
                <div className="absolute top-16 left-4 right-4 text-xs text-gray-400 pointer-events-none">
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg border border-yellow-200/50">
                    <div className="flex items-start">
                      <span className="material-icons text-sm text-yellow-600 mr-2 mt-0.5">lightbulb</span>
                      <div>
                        <p className="font-medium text-yellow-700 mb-1">提示词编写建议：</p>
                        <ul className="text-yellow-600 space-y-1">
                          <li>• 明确说明AI的角色和任务</li>
                          <li>• 提供具体的输出格式要求</li>
                          <li>• 包含相关的示例或参考</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
}
