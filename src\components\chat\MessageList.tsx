'use client';

import React from 'react';
import { MessageItem } from './MessageItem';

// 聊天消息接口
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface MessageListProps {
  messages: ChatMessage[];
}

export const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  if (messages.length === 0) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center max-w-sm">
          {/* 装饰性图标 */}
          <div className="relative mb-6">
            <div className="w-20 h-20 mx-auto rounded-3xl bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] flex items-center justify-center text-white shadow-lg opacity-80">
              <span className="material-icons text-3xl">chat_bubble_outline</span>
            </div>
            {/* 装饰性小点 */}
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#00C250] rounded-full flex items-center justify-center">
              <span className="material-icons text-white text-sm">auto_awesome</span>
            </div>
          </div>

          <h3 className="text-xl font-medium text-text-dark mb-3" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>
            开始对话
          </h3>
          <p className="text-text-medium text-sm leading-relaxed">
            向AI发送消息开始智能对话<br/>
            获得创作灵感和写作建议
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="px-6 py-[10px] space-y-6">
      {messages.map((message) => (
        <MessageItem
          key={message.id}
          message={message}
        />
      ))}
    </div>
  );
};
