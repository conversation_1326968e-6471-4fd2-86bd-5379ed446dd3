/**
 * 提示词卡片组件
 */
import React from 'react';
// 本地提示词类型已删除
import { Card } from '@/components/common';
import { useAuthStore } from '@/store/slices/authStore';

interface PromptTypeInfo {
  label: string;
  color: string;
  icon: string;
  group: string;
  gradient: string;
}

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptCardProps {
  prompt: UserPrompt;
  typeInfo: PromptTypeInfo;
  onClick: () => void;
  onDelete?: (e: React.MouseEvent) => void;
  isOwner?: boolean;
}

/**
 * 提示词卡片组件
 */
export const PromptCard: React.FC<PromptCardProps> = ({
  prompt,
  typeInfo,
  onClick,
  onDelete,
  isOwner = false
}) => {
  // 提取颜色代码用于胶带
  const tapeColor = typeInfo.color.split(' ')[1].replace('text-', 'rgba(').replace(/\]/, ', 0.7)');

  return (
    <Card
      className="group p-6 cursor-pointer hover:shadow-xl transition-all duration-300 relative transform hover:-translate-y-1 hover:scale-[1.02]"
      onClick={onClick}
      tapeColor={tapeColor}
      withPageCurl={true}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.02)] to-[rgba(90,157,107,0.04)] opacity-60 rounded-[22px]"></div>
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[rgba(120,180,140,0.08)] to-transparent rounded-full transform translate-x-12 -translate-y-12"></div>

      <div className="relative z-10 flex flex-col h-full">
        {/* 头部区域 */}
        <div className="flex items-start mb-4">
          <div className={`relative w-12 h-12 rounded-2xl flex-shrink-0 flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-all duration-300 ${typeInfo.color.split(' ')[0]}`}>
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
            <span className={`material-icons text-xl ${typeInfo.color.split(' ')[1]} relative z-10 group-hover:scale-110 transition-transform duration-300`}>
              {typeInfo.icon}
            </span>
            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5 rounded-2xl"></div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <h3 className="text-gray-800 font-semibold text-lg leading-tight mb-2 group-hover:text-gray-900 transition-colors duration-300 font-ma-shan">
                {prompt.title}
              </h3>
              {isOwner && onDelete && (
                <button
                  onClick={onDelete}
                  className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all duration-300 transform hover:scale-110"
                  title="删除"
                >
                  <span className="material-icons text-sm">delete</span>
                </button>
              )}
            </div>

            <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${
              typeInfo.color.includes('5a9d6b')
                ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border border-green-200/50'
                : 'bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 border border-purple-200/50'
            }`}>
              <span className="material-icons text-xs mr-1">
                {typeInfo.icon}
              </span>
              {typeInfo.label}
            </div>
          </div>
        </div>

        {/* 描述内容 */}
        <div className="flex-1 mb-4">
          {prompt.description ? (
            <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 group-hover:text-gray-700 transition-colors duration-300">
              {prompt.description}
            </p>
          ) : (
            <p className="text-gray-400 text-sm italic leading-relaxed">
              这个提示词暂时没有描述信息...
            </p>
          )}
        </div>

        {/* 底部信息 */}
        <div className="mt-auto pt-4 border-t border-gray-200/60">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center">
              <div className="w-5 h-5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mr-2">
                <span className="material-icons text-xs text-gray-600">schedule</span>
              </div>
              <span className="font-medium">
                {new Date(prompt.updated_at).toLocaleDateString('zh-CN', {
                  month: 'short',
                  day: 'numeric'
                })}
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mr-2">
                <span className="material-icons text-xs text-blue-600">person</span>
              </div>
              <span className="font-medium truncate max-w-20">
                {prompt.created_by || '未知用户'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};






